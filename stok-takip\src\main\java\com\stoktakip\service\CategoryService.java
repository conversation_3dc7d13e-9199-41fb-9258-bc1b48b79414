package com.stoktakip.service;

import com.stoktakip.model.Category;
import com.stoktakip.repository.CategoryRepository;
import jakarta.persistence.EntityExistsException;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CategoryService {

    private final CategoryRepository categoryRepository;

    @Autowired
    public CategoryService(CategoryRepository categoryRepository) {
        this.categoryRepository = categoryRepository;
    }

    public List<Category> findAll() {
        return categoryRepository.findAll();
    }

    public Category findById(Long id) {
        return categoryRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Kategori bulunamadı: " + id));
    }

    public Category findByName(String name) {
        return categoryRepository.findByName(name)
                .orElseThrow(() -> new EntityNotFoundException("Kategori bulunamadı: " + name));
    }

    @Transactional
    public Category save(Category category) {
        if (category.getId() != null && categoryRepository.existsById(category.getId())) {
            throw new EntityExistsException("Bu ID ile kategori zaten mevcut: " + category.getId());
        }

        if (categoryRepository.existsByName(category.getName())) {
            throw new EntityExistsException("Bu isimle kategori zaten mevcut: " + category.getName());
        }

        return categoryRepository.save(category);
    }

    @Transactional
    public Category update(Category category) {
        if (category.getId() == null || !categoryRepository.existsById(category.getId())) {
            throw new EntityNotFoundException("Güncellenecek kategori bulunamadı: " + category.getId());
        }

        categoryRepository.findByName(category.getName())
                .ifPresent(existingCategory -> {
                    if (!existingCategory.getId().equals(category.getId())) {
                        throw new EntityExistsException("Bu isimle başka bir kategori zaten mevcut: " + category.getName());
                    }
                });

        return categoryRepository.save(category);
    }

    @Transactional
    public void delete(Long id) {
        Category category = findById(id);
        if (!category.getProducts().isEmpty()) {
            throw new IllegalStateException("Bu kategoriye ait ürünler bulunmaktadır. Önce ürünleri silmelisiniz.");
        }
        categoryRepository.delete(category);
    }
}
