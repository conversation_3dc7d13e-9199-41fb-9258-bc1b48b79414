package com.stoktakip.repository;

import com.stoktakip.model.Product;
import com.stoktakip.model.StockMovement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface StockMovementRepository extends JpaRepository<StockMovement, Long> {
    List<StockMovement> findByProduct(Product product);
    List<StockMovement> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<StockMovement> findByProductAndDateBetween(Product product, LocalDateTime startDate, LocalDateTime endDate);
    List<StockMovement> findByMovementType(StockMovement.MovementType movementType);
}
