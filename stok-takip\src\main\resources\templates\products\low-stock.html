<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Düşük Stok - Stok Takip Sistemi</title>
    <style>
        /* Düşük Stok Sayfası Özel CSS */
        .low-stock-container {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .low-stock-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .low-stock-title {
            color: #2d3436;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .low-stock-title i {
            margin-right: 1rem;
            color: #fd79a8;
        }

        .alert-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .alert-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .filter-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .filter-header {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .filter-body {
            padding: 2rem;
        }

        .filter-input {
            border: 2px solid #ddd;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .filter-input:focus {
            border-color: #fd79a8;
            box-shadow: 0 0 0 3px rgba(253, 121, 168, 0.1);
            background: white;
        }

        .filter-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(253, 121, 168, 0.4);
        }

        .low-stock-table-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-enhanced {
            margin: 0;
            background: transparent;
        }

        .table-enhanced thead {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
        }

        .table-enhanced thead th {
            border: none;
            padding: 1.5rem 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .table-enhanced tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table-enhanced tbody tr:hover {
            background: rgba(253, 121, 168, 0.1);
            transform: scale(1.01);
        }

        .table-enhanced tbody td {
            padding: 1.5rem 1rem;
            vertical-align: middle;
            border: none;
        }

        .product-code {
            font-family: 'Courier New', monospace;
            background: rgba(253, 121, 168, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
            font-weight: 600;
            color: #e84393;
        }

        .product-name {
            font-weight: 600;
            color: #2d3436;
            font-size: 1.1rem;
        }

        .category-badge {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stock-critical {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            min-width: 60px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .action-btn-add {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }

        .action-btn-edit {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #636e72;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #2d3436;
        }

        /* Animasyonlar */
        .low-stock-header {
            animation: slideInDown 0.6s ease-out;
        }

        .filter-card {
            animation: slideInLeft 0.6s ease-out;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .low-stock-table-card {
            animation: slideInUp 0.6s ease-out;
            animation-delay: 0.4s;
            animation-fill-mode: both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .low-stock-container {
                padding: 1rem;
            }

            .low-stock-title {
                font-size: 2rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="low-stock-container">
            <div class="container">
                <!-- Header -->
                <div class="low-stock-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="low-stock-title">
                            <i class="fas fa-exclamation-triangle"></i>Düşük Stok Uyarısı
                        </h1>
                        <a th:href="@{/stock-movements/new}" class="alert-btn">
                            <i class="fas fa-plus me-2"></i>Stok Ekle
                        </a>
                    </div>
                </div>

                <!-- Filtre -->
                <div class="filter-card">
                    <div class="filter-header">
                        <i class="fas fa-filter me-2"></i>Filtreleme Seçenekleri
                    </div>
                    <div class="filter-body">
                        <form th:action="@{/products/low-stock}" method="get">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-folder me-1"></i>Kategori
                                    </label>
                                    <select class="form-control filter-input" id="category" name="categoryId">
                                        <option value="">Tüm Kategoriler</option>
                                        <option th:each="cat : ${categories}" th:value="${cat.id}" th:text="${cat.name}"
                                                th:selected="${categoryId != null && categoryId == cat.id}">Kategori</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="threshold" class="form-label">
                                        <i class="fas fa-chart-line me-1"></i>Eşik Değeri
                                    </label>
                                    <input type="number" class="form-control filter-input" id="threshold" name="threshold" min="1"
                                           th:value="${threshold != null ? threshold : 10}" placeholder="10">
                                </div>
                                <div class="col-md-4 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn filter-btn w-100">
                                        <i class="fas fa-search me-2"></i>Filtrele
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tablo -->
                <div class="low-stock-table-card">
                    <div th:if="${products.empty}" class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <h3>Harika!</h3>
                        <p>Düşük stok ürün bulunmamaktadır.</p>
                        <a th:href="@{/products}" class="alert-btn">
                            <i class="fas fa-boxes me-2"></i>Tüm Ürünleri Görüntüle
                        </a>
                    </div>

                    <div th:unless="${products.empty}" class="table-responsive">
                        <table class="table table-enhanced">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ürün Kodu</th>
                                    <th>Ürün Adı</th>
                                    <th>Kategori</th>
                                    <th>Kritik Stok</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="product : ${products}">
                                    <td>
                                        <strong th:text="${product.id}">1</strong>
                                    </td>
                                    <td>
                                        <span class="product-code" th:text="${product.code}">ABC123</span>
                                    </td>
                                    <td>
                                        <div class="product-name" th:text="${product.name}">Ürün Adı</div>
                                        <small class="text-muted" th:text="${product.description}" th:if="${product.description}">Açıklama</small>
                                    </td>
                                    <td>
                                        <span class="category-badge"
                                              th:text="${product.category != null ? product.category.name : 'Kategorisiz'}"
                                              th:if="${product.category != null}">Kategori</span>
                                        <span class="text-muted" th:unless="${product.category != null}">Kategorisiz</span>
                                    </td>
                                    <td>
                                        <span class="stock-critical" th:text="${product.stockQuantity}">0</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a th:href="@{/stock-movements/new(productId=${product.id})}"
                                               class="action-btn action-btn-add"
                                               title="Stok Ekle">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                            <a th:href="@{/products/edit/{id}(id=${product.id})}"
                                               class="action-btn action-btn-edit"
                                               title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
