<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Profil - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1>Profil</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a th:href="@{/profile/change-password}" class="btn btn-sm btn-primary">
                    <i class="bi bi-key"></i> <PERSON>ifre <PERSON>
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form th:action="@{/profile/update}" th:object="${user}" method="post">
                            <input type="hidden" th:field="*{id}">
                            <input type="hidden" th:field="*{username}">
                            <input type="hidden" th:field="*{password}">
                            <input type="hidden" th:field="*{active}">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Kullanıcı Adı</label>
                                <input type="text" class="form-control" id="username" th:value="*{username}" disabled>
                            </div>
                            
                            <div class="mb-3">
                                <label for="firstName" class="form-label">Ad</label>
                                <input type="text" class="form-control" id="firstName" th:field="*{firstName}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="lastName" class="form-label">Soyad</label>
                                <input type="text" class="form-control" id="lastName" th:field="*{lastName}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta</label>
                                <input type="email" class="form-control" id="email" th:field="*{email}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/dashboard}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
