<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Ürünler - Stok Takip Sistemi</title>
    <style>
        /* Ürün Listesi Sayfası Özel CSS */
        .products-container {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 2rem 0;
            border-radius: 20px;
            align-self: auto;
            margin: auto;
        }

        .products-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .products-title {
            color: #2d3436;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .products-title i {
            margin-right: 1rem;
            color: #74b9ff;
        }

        .add-product-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .add-product-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .search-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .search-header {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .search-body {
            padding: 2rem;
        }

        .search-input {
            border: 2px solid #ddd;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .search-input:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
            background: white;
        }

        .search-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }

        .products-table-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-enhanced {
            margin: 0;
            background: transparent;
        }

        .table-enhanced thead {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
        }

        .table-enhanced thead th {
            border: none;
            padding: 1.5rem 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .table-enhanced tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table-enhanced tbody tr:hover {
            background: rgba(116, 185, 255, 0.1);
            transform: scale(1.01);
        }

        .table-enhanced tbody td {
            padding: 1.5rem 1rem;
            vertical-align: middle;
            border: none;
        }

        .product-code {
            font-family: 'Courier New', monospace;
            background: rgba(116, 185, 255, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
            font-weight: 600;
            color: #0984e3;
        }

        .product-name {
            font-weight: 600;
            color: #2d3436;
            font-size: 1.1rem;
        }

        .category-badge {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .price-display {
            font-size: 1.2rem;
            font-weight: 700;
            color: #00b894;
        }

        .stock-badge {
            padding: 0.75rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            min-width: 60px;
            text-align: center;
        }

        .stock-high {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }

        .stock-medium {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .stock-low {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .action-btn-edit {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .action-btn-delete {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
          
        }

        .action-btn-stock {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #636e72;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #2d3436;
        }

        /* Animasyonlar */
        .products-header {
            animation: slideInDown 0.6s ease-out;
        }

        .search-card {
            animation: slideInLeft 0.6s ease-out;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .products-table-card {
            animation: slideInUp 0.6s ease-out;
            animation-delay: 0.4s;
            animation-fill-mode: both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .products-container {
                padding: 1rem;
            }

            .products-title {
                font-size: 2rem;
            }

            .action-buttons {
                flex-direction: column;
                
            }

            .table-responsive {
                border-radius: 20px;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="products-container">
            <div class="container">
                <!-- Header -->
                <div class="products-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="products-title">
                            <i class="fas fa-boxes"></i>Ürün Yönetimi
                        </h1>
                        <a th:href="@{/products/new}" class="add-product-btn">
                            <i class="fas fa-plus me-2"></i>Yeni Ürün Ekle
                        </a>
                    </div>
                </div>

                <!-- Arama Formu -->
                <div class="search-card">
                    <div class="search-header">
                        <i class="fas fa-search me-2"></i>Ürün Arama
                    </div>
                    <div class="search-body">
                        <form th:action="@{/products/search}" method="get" class="row g-3">
                            <div class="col-md-8">
                                <input type="text"
                                       class="form-control search-input"
                                       id="query"
                                       name="query"
                                       placeholder="Ürün adı, kodu veya açıklama ara...">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn search-btn w-100">
                                    <i class="fas fa-search me-2"></i>Ara
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Ürün Tablosu -->
                <div class="products-table-card">
                    <div th:if="${products.empty}" class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>Henüz ürün bulunmamaktadır</h3>
                        <p>Sisteme ilk ürününüzü ekleyerek başlayın.</p>
                        <a th:href="@{/products/new}" class="add-product-btn">
                            <i class="fas fa-plus me-2"></i>İlk Ürünü Ekle
                        </a>
                    </div>

                    <div th:unless="${products.empty}" class="table-responsive">
                        <table class="table table-enhanced">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ürün Kodu</th>
                                    <th>Ürün Adı</th>
                                    <th>Kategori</th>
                                    <th>Fiyat</th>
                                    <th>Stok</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="product : ${products}">
                                    <td>
                                        <strong th:text="${product.id}">1</strong>
                                    </td>
                                    <td>
                                        <span class="product-code" th:text="${product.code}">ABC123</span>
                                    </td>
                                    <td>
                                        <div class="product-name" th:text="${product.name}">Ürün Adı</div>
                                        <small class="text-muted" th:text="${product.description}" th:if="${product.description}">Açıklama</small>
                                    </td>
                                    <td>
                                        <span class="category-badge"
                                              th:text="${product.category != null ? product.category.name : 'Kategorisiz'}"
                                              th:if="${product.category != null}">Kategori</span>
                                        <span class="text-muted" th:unless="${product.category != null}">Kategorisiz</span>
                                    </td>
                                    <td>
                                        <div class="price-display" th:text="${#numbers.formatDecimal(product.price, 1, 'POINT', 2, 'COMMA') + ' ₺'}">0.00 ₺</div>
                                    </td>
                                    <td>
                                        <span th:class="${product.stockQuantity >= 20 ? 'stock-badge stock-high' :
                                                        (product.stockQuantity >= 10 ? 'stock-badge stock-medium' : 'stock-badge stock-low')}"
                                              th:text="${product.stockQuantity}">0</span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a th:href="@{/products/edit/{id}(id=${product.id})}"
                                               class="action-btn action-btn-edit"
                                               title="Düzenle">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a th:href="@{/stock-movements/product/{id}(id=${product.id})}"
                                               class="action-btn action-btn-stock"
                                               title="Stok Hareketleri">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                            <a th:href="@{/products/delete/{id}(id=${product.id})}"
                                               class="action-btn action-btn-delete"
                                               title="Sil"
                                               onclick="return confirm('Bu ürünü silmek istediğinize emin misiniz?')">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
