<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:fragment="head(title)">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: 'Stok Takip Sistemi'">Stok Takip Sistemi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav th:fragment="navbar" class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" th:href="@{/}">Stok Takip Sistemi</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto" sec:authorize="isAuthenticated()">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <span sec:authentication="name">Kullanıcı</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" th:href="@{/profile}">Profil</a></li>
                            <li><a class="dropdown-item" th:href="@{/profile/change-password}">Şifre Değiştir</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post">
                                    <button type="submit" class="dropdown-item">Çıkış Yap</button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div th:fragment="sidebar" class="col-md-3 col-lg-2 d-md-block sidebar collapse" sec:authorize="isAuthenticated()">
        <div class="position-sticky pt-3">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" th:classappend="${activeTab == 'dashboard' ? 'active' : ''}" th:href="@{/dashboard}">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:classappend="${activeTab == 'products' ? 'active' : ''}" th:href="@{/products}">
                        <i class="bi bi-box-seam"></i> Ürünler
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:classappend="${activeTab == 'categories' ? 'active' : ''}" th:href="@{/categories}">
                        <i class="bi bi-tags"></i> Kategoriler
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:classappend="${activeTab == 'stock-movements' ? 'active' : ''}" th:href="@{/stock-movements}">
                        <i class="bi bi-arrow-left-right"></i> Stok Hareketleri
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:classappend="${activeTab == 'low-stock' ? 'active' : ''}" th:href="@{/products/low-stock}">
                        <i class="bi bi-exclamation-triangle"></i> Düşük Stok
                    </a>
                </li>
                <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                    <a class="nav-link" th:classappend="${activeTab == 'users' ? 'active' : ''}" th:href="@{/admin/users}">
                        <i class="bi bi-people"></i> Kullanıcı Yönetimi
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Scripts -->
    <div th:fragment="scripts">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    </div>
</body>
</html>
