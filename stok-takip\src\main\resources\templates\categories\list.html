<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Kategoriler - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-tags me-2"></i>Kategoriler
            </h1>
            <a th:href="@{/categories/new}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Yeni <PERSON><PERSON>
            </a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i><PERSON><PERSON><PERSON>
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${categories.empty}" class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Henüz kategori bulunmamaktadır.
                    <a th:href="@{/categories/new}" class="alert-link">İlk kategoriyi ekleyin</a>.
                </div>

                <div th:unless="${categories.empty}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Kategori Adı</th>
                                <th>Açıklama</th>
                                <th>Ürün Sayısı</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="category : ${categories}">
                                <td th:text="${category.id}">1</td>
                                <td>
                                    <strong th:text="${category.name}">Kategori Adı</strong>
                                </td>
                                <td th:text="${category.description ?: 'Açıklama yok'}">Açıklama</td>
                                <td>
                                    <span class="badge bg-primary" th:text="${category.products.size()}">0</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/categories/edit/{id}(id=${category.id})}"
                                           class="btn btn-sm btn-outline-primary"
                                           title="Düzenle">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a th:href="@{/products/category/{id}(id=${category.id})}"
                                           class="btn btn-sm btn-outline-info"
                                           title="Ürünleri Görüntüle">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a th:href="@{/categories/delete/{id}(id=${category.id})}"
                                           class="btn btn-sm btn-outline-danger"
                                           title="Sil"
                                           onclick="return confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
