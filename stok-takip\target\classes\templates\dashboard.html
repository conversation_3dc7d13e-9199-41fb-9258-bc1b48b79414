<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Dashboard - Stok Takip Sistemi</title>
    <style>
        /* Dashboard Sayfası Özel CSS */
        .dashboard-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2.3rem 2rem;
            border-radius: 30px;
        }

        .dashboard-welcome {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .dashboard-title {
            color: #2d3436;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dashboard-subtitle {
            color: #636e72;
            font-size: 1.2rem;
            margin-bottom: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient);
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card.products::before {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .stat-card.low-stock::before {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }

        .stat-card.quick-actions::before {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .stat-card.products .stat-icon {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .stat-card.low-stock .stat-icon {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        }

        .stat-card.quick-actions .stat-icon {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .stat-title {
            font-size: 1.1rem;
            color: #636e72;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 1rem;
        }

        .stat-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
        }

        .stat-link:hover {
            color: #764ba2;
            transform: translateX(5px);
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .quick-action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .quick-action-btn.add-product {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .quick-action-btn.add-movement {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .content-card-header {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-card-body {
            padding: 2rem;
        }

        .chart-container {
            grid-column: 1 / -1;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .chart-header {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .chart-body {
            padding: 2rem;
        }

        .table-enhanced {
            margin: 0;
        }

        .table-enhanced thead {
            background: linear-gradient(135deg, #ddd 0%, #bbb 100%);
        }

        .table-enhanced tbody tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .badge-enhanced {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
        }

        .badge-danger-enhanced {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }

        .badge-success-enhanced {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }

        .badge-info-enhanced {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .view-all-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .view-all-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #636e72;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ddd;
        }

        /* Animasyonlar */
        .dashboard-welcome {
            animation: slideInDown 0.6s ease-out;
        }

        .stat-card {
            animation: slideInUp 0.6s ease-out;
            animation-fill-mode: both;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }

        .chart-card {
            animation: fadeIn 0.6s ease-out;
            animation-delay: 0.4s;
            animation-fill-mode: both;
        }

        .content-card {
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .content-card:nth-child(1) { animation-delay: 0.5s; }
        .content-card:nth-child(2) { animation-delay: 0.6s; }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }

            .dashboard-title {
                font-size: 2rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="dashboard-container">
            <div class="container">
                <!-- Welcome Section -->
                <div class="dashboard-welcome">
                    <h1 class="dashboard-title">
                        <i class="fas fa-tachometer-alt me-3"></i>Dashboard
                    </h1>
                    <p class="dashboard-subtitle">Stok takip sisteminize hoş geldiniz</p>
                </div>

                <!-- İstatistik Kartları -->
                <div class="stats-grid">
                    <div class="stat-card products">
                        <div class="stat-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-title">Toplam Ürün</div>
                        <div class="stat-value" th:text="${totalProducts}">0</div>
                        <a th:href="@{/products}" class="stat-link">
                            <i class="fas fa-arrow-right me-1"></i>Ürünleri Görüntüle
                        </a>
                    </div>

                    <div class="stat-card low-stock">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-title">Düşük Stok Ürünler</div>
                        <div class="stat-value" th:text="${lowStockProducts.size()}">0</div>
                        <a th:href="@{/products/low-stock}" class="stat-link">
                            <i class="fas fa-arrow-right me-1"></i>Düşük Stok Ürünleri
                        </a>
                    </div>

                    <div class="stat-card quick-actions">
                        <div class="stat-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="stat-title">Hızlı İşlemler</div>
                        <div class="quick-actions-grid">
                            <a th:href="@{/products/new}" class="quick-action-btn add-product">
                                <i class="fas fa-plus"></i>Ürün Ekle
                            </a>
                            <a th:href="@{/stock-movements/new}" class="quick-action-btn add-movement">
                                <i class="fas fa-exchange-alt"></i>Stok Hareketi
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Stok Grafiği -->
                <div class="chart-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <i class="fas fa-chart-bar me-2"></i>Stok Durumu Grafiği
                        </div>
                        <div class="chart-body">
                            <canvas id="stockChart" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Düşük Stok Ürünler ve Son Stok Hareketleri -->
                <div class="content-grid">
                    <div class="content-card">
                        <div class="content-card-header">
                            <span><i class="fas fa-exclamation-circle me-2"></i>Düşük Stok Ürünler</span>
                            <a th:href="@{/products/low-stock}" class="view-all-btn">
                                <i class="fas fa-eye me-1"></i>Tümünü Gör
                            </a>
                        </div>
                        <div class="content-card-body">
                            <div th:if="${lowStockProducts.empty}" class="empty-state">
                                <i class="fas fa-check-circle"></i>
                                <h5>Harika!</h5>
                                <p>Düşük stok ürün bulunmamaktadır.</p>
                            </div>
                            <div th:unless="${lowStockProducts.empty}" class="table-responsive">
                                <table class="table table-enhanced">
                                    <thead>
                                        <tr>
                                            <th>Ürün</th>
                                            <th>Stok</th>
                                            <th>İşlem</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="product : ${lowStockProducts}">
                                            <td>
                                                <div>
                                                    <strong th:text="${product.name}">Ürün Adı</strong>
                                                    <br>
                                                    <small class="text-muted" th:text="${product.code}">Kod</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge-enhanced badge-danger-enhanced" th:text="${product.stockQuantity}">0</span>
                                            </td>
                                            <td>
                                                <a th:href="@{/stock-movements/new}" class="quick-action-btn add-movement" style="padding: 0.5rem 1rem; font-size: 0.8rem;">
                                                    <i class="fas fa-plus me-1"></i>Stok Ekle
                                                </a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="content-card">
                        <div class="content-card-header">
                            <span><i class="fas fa-history me-2"></i>Son Stok Hareketleri</span>
                            <a th:href="@{/stock-movements}" class="view-all-btn">
                                <i class="fas fa-eye me-1"></i>Tümünü Gör
                            </a>
                        </div>
                        <div class="content-card-body">
                            <div th:if="${recentMovements.empty}" class="empty-state">
                                <i class="fas fa-info-circle"></i>
                                <h5>Henüz hareket yok</h5>
                                <p>İlk stok hareketinizi ekleyin.</p>
                            </div>
                            <div th:unless="${recentMovements.empty}" class="table-responsive">
                                <table class="table table-enhanced">
                                    <thead>
                                        <tr>
                                            <th>Ürün</th>
                                            <th>Hareket</th>
                                            <th>Miktar</th>
                                            <th>Tarih</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="movement : ${recentMovements}">
                                            <td>
                                                <strong th:text="${movement.product.name}">Ürün Adı</strong>
                                            </td>
                                            <td>
                                                <span th:if="${movement.movementType.name() == 'IN'}" class="badge-enhanced badge-success-enhanced">
                                                    <i class="fas fa-arrow-down me-1"></i>Giriş
                                                </span>
                                                <span th:if="${movement.movementType.name() == 'OUT'}" class="badge-enhanced badge-danger-enhanced">
                                                    <i class="fas fa-arrow-up me-1"></i>Çıkış
                                                </span>
                                            </td>
                                            <td>
                                                <strong th:text="${movement.quantity}">0</strong>
                                            </td>
                                            <td>
                                                <small th:text="${#temporals.format(movement.date, 'dd.MM.yyyy HH:mm')}">01.01.2023</small>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block layout:fragment="scripts">
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Chart initialization for dashboard
                const chartCanvas = document.getElementById('stockChart');
                if (chartCanvas && typeof Chart !== 'undefined') {
                    const ctx = chartCanvas.getContext('2d');

                    // Sample data - in a real app, this would come from the server
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Laptop', 'Akıllı Telefon', 'Süt', 'Tükenmez Kalem'],
                            datasets: [{
                                label: 'Stok Miktarı',
                                data: [5, 10, 50, 100],
                                backgroundColor: [
                                    'rgba(52, 152, 219, 0.7)',
                                    'rgba(46, 204, 113, 0.7)',
                                    'rgba(155, 89, 182, 0.7)',
                                    'rgba(241, 196, 15, 0.7)'
                                ],
                                borderColor: [
                                    'rgba(52, 152, 219, 1)',
                                    'rgba(46, 204, 113, 1)',
                                    'rgba(155, 89, 182, 1)',
                                    'rgba(241, 196, 15, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            });
        </script>
    </th:block>
</body>
</html>
