# H2 Veritabanı Ayarları
spring.datasource.url=jdbc:h2:file:./stokdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate Ayarları
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Thymeleaf Ayarları
spring.thymeleaf.cache=false

# Server Port
server.port=8080

# Logging
logging.level.org.springframework.security=DEBUG
logging.level.com.stoktakip=DEBUG

# Static Resources
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
