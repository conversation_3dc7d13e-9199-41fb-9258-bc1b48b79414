package com.stoktakip.config;

import com.stoktakip.model.Category;
import com.stoktakip.model.Product;
import com.stoktakip.model.StockMovement;
import com.stoktakip.model.User;
import com.stoktakip.repository.CategoryRepository;
import com.stoktakip.repository.ProductRepository;
import com.stoktakip.repository.StockMovementRepository;
import com.stoktakip.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

@Component
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final CategoryRepository categoryRepository;
    private final ProductRepository productRepository;
    private final StockMovementRepository stockMovementRepository;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public DataInitializer(UserRepository userRepository,
                          CategoryRepository categoryRepository,
                          ProductRepository productRepository,
                          StockMovementRepository stockMovementRepository,
                          PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.categoryRepository = categoryRepository;
        this.productRepository = productRepository;
        this.stockMovementRepository = stockMovementRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional
    public void run(String... args) {
        // Admin kullanıcısı oluştur
        if (!userRepository.existsByUsername("admin")) {
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin"));
            admin.setFirstName("Admin");
            admin.setLastName("User");
            admin.setEmail("<EMAIL>");
            admin.setActive(true);
            admin.setRoles(Set.of("ROLE_USER", "ROLE_ADMIN"));
            userRepository.save(admin);

            System.out.println("Admin kullanıcısı oluşturuldu: admin / admin");
        }

        // Normal kullanıcı oluştur
        if (!userRepository.existsByUsername("user")) {
            User user = new User();
            user.setUsername("user");
            user.setPassword(passwordEncoder.encode("user"));
            user.setFirstName("Normal");
            user.setLastName("User");
            user.setEmail("<EMAIL>");
            user.setActive(true);
            user.setRoles(Set.of("ROLE_USER"));
            userRepository.save(user);

            System.out.println("Normal kullanıcı oluşturuldu: user / user");
        }

        // Örnek kategoriler oluştur
        if (categoryRepository.count() == 0) {
            Category elektronik = new Category();
            elektronik.setName("Elektronik");
            elektronik.setDescription("Elektronik ürünler");
            categoryRepository.save(elektronik);

            Category gida = new Category();
            gida.setName("Gıda");
            gida.setDescription("Gıda ürünleri");
            categoryRepository.save(gida);

            Category kirtasiye = new Category();
            kirtasiye.setName("Kırtasiye");
            kirtasiye.setDescription("Kırtasiye ürünleri");
            categoryRepository.save(kirtasiye);

            System.out.println("Örnek kategoriler oluşturuldu");

            // Örnek ürünler oluştur
            if (productRepository.count() == 0) {
                Product laptop = new Product();
                laptop.setName("Laptop");
                laptop.setDescription("Yüksek performanslı laptop");
                laptop.setCode("ELK001");
                laptop.setPrice(new BigDecimal("15000.00"));
                laptop.setStockQuantity(5);
                laptop.setCategory(elektronik);
                productRepository.save(laptop);

                Product telefon = new Product();
                telefon.setName("Akıllı Telefon");
                telefon.setDescription("Son model akıllı telefon");
                telefon.setCode("ELK002");
                telefon.setPrice(new BigDecimal("8000.00"));
                telefon.setStockQuantity(10);
                telefon.setCategory(elektronik);
                productRepository.save(telefon);

                Product sut = new Product();
                sut.setName("Süt");
                sut.setDescription("1 litre süt");
                sut.setCode("GID001");
                sut.setPrice(new BigDecimal("15.90"));
                sut.setStockQuantity(50);
                sut.setCategory(gida);
                productRepository.save(sut);

                Product kalem = new Product();
                kalem.setName("Tükenmez Kalem");
                kalem.setDescription("Mavi tükenmez kalem");
                kalem.setCode("KIR001");
                kalem.setPrice(new BigDecimal("5.50"));
                kalem.setStockQuantity(100);
                kalem.setCategory(kirtasiye);
                productRepository.save(kalem);

                System.out.println("Örnek ürünler oluşturuldu");

                // Örnek stok hareketleri oluştur
                if (stockMovementRepository.count() == 0) {
                    User adminUser = userRepository.findByUsername("admin").orElseThrow();

                    StockMovement laptopGiris = new StockMovement();
                    laptopGiris.setProduct(laptop);
                    laptopGiris.setMovementType(StockMovement.MovementType.IN);
                    laptopGiris.setQuantity(5);
                    laptopGiris.setDate(LocalDateTime.now().minusDays(5));
                    laptopGiris.setUser(adminUser);
                    laptopGiris.setNote("İlk stok girişi");
                    stockMovementRepository.save(laptopGiris);

                    StockMovement telefonGiris = new StockMovement();
                    telefonGiris.setProduct(telefon);
                    telefonGiris.setMovementType(StockMovement.MovementType.IN);
                    telefonGiris.setQuantity(10);
                    telefonGiris.setDate(LocalDateTime.now().minusDays(4));
                    telefonGiris.setUser(adminUser);
                    telefonGiris.setNote("İlk stok girişi");
                    stockMovementRepository.save(telefonGiris);

                    StockMovement sutGiris = new StockMovement();
                    sutGiris.setProduct(sut);
                    sutGiris.setMovementType(StockMovement.MovementType.IN);
                    sutGiris.setQuantity(50);
                    sutGiris.setDate(LocalDateTime.now().minusDays(3));
                    sutGiris.setUser(adminUser);
                    sutGiris.setNote("İlk stok girişi");
                    stockMovementRepository.save(sutGiris);

                    StockMovement kalemGiris = new StockMovement();
                    kalemGiris.setProduct(kalem);
                    kalemGiris.setMovementType(StockMovement.MovementType.IN);
                    kalemGiris.setQuantity(100);
                    kalemGiris.setDate(LocalDateTime.now().minusDays(2));
                    kalemGiris.setUser(adminUser);
                    kalemGiris.setNote("İlk stok girişi");
                    stockMovementRepository.save(kalemGiris);

                    System.out.println("Örnek stok hareketleri oluşturuldu");
                }
            }
        }
    }
}
