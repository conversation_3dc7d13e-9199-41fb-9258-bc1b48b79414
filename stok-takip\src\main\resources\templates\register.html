<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Ol - Stok Takip Sistemi</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <style>
        body {
            background: linear-gradient(135deg, #3498db, #2c3e50);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .register-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            padding: 40px;
            width: 100%;
            max-width: 500px;
        }

        .register-logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .register-logo i {
            font-size: 3rem;
            color: #3498db;
        }

        .register-title {
            text-align: center;
            margin-bottom: 20px;
        }

        .register-form .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .register-form .form-control {
            height: 45px;
            border-radius: 5px;
            padding-left: 45px;
        }

        .register-form .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .register-form .input-icon {
            position: absolute;
            left: 15px;
            top: 14px;
            color: #3498db;
        }

        .register-btn {
            height: 45px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 5px;
            background-color: #3498db;
            border-color: #3498db;
            transition: all 0.3s;
        }

        .register-btn:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-2px);
        }

        .register-footer {
            text-align: center;
            margin-top: 20px;
        }

        .register-footer a {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
        }

        .register-footer a:hover {
            text-decoration: underline;
        }

        .validation-error {
            color: #e74c3c;
            font-size: 0.85rem;
            margin-top: 5px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-logo">
            <i class="fas fa-user-plus"></i>
        </div>

        <div class="register-title">
            <h1 class="h3 fw-bold">Stok Takip Sistemi</h1>
            <p class="text-muted">Yeni hesap oluşturun</p>
        </div>

        <!-- Alerts -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- Register Form -->
        <form class="register-form" th:action="@{/register}" method="post" th:object="${user}">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group position-relative">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="firstName" th:field="*{firstName}" placeholder="Ad" required>
                        <span class="validation-error" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}"></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group position-relative">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="lastName" th:field="*{lastName}" placeholder="Soyad" required>
                        <span class="validation-error" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}"></span>
                    </div>
                </div>
            </div>

            <div class="form-group position-relative">
                <i class="fas fa-user-tag input-icon"></i>
                <input type="text" class="form-control" id="username" th:field="*{username}" placeholder="Kullanıcı Adı" required>
                <span class="validation-error" th:if="${#fields.hasErrors('username')}" th:errors="*{username}"></span>
            </div>

            <div class="form-group position-relative">
                <i class="fas fa-envelope input-icon"></i>
                <input type="email" class="form-control" id="email" th:field="*{email}" placeholder="E-posta" required>
                <span class="validation-error" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></span>
            </div>

            <div class="form-group position-relative">
                <i class="fas fa-lock input-icon"></i>
                <input type="password" class="form-control" id="password" th:field="*{password}" placeholder="Şifre" required>
                <span class="validation-error" th:if="${#fields.hasErrors('password')}" th:errors="*{password}"></span>
            </div>

            <button type="submit" class="btn btn-primary w-100 register-btn">
                <i class="fas fa-user-plus me-2"></i>Kayıt Ol
            </button>
        </form>

        <div class="register-footer">
            <p>Zaten hesabınız var mı? <a th:href="@{/login}">Giriş Yap</a></p>
            <p class="text-muted mt-3">&copy; 2025 Stok Takip Sistemi</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
