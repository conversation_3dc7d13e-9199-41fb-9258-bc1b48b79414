<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title th:text="${user.id == null ? 'Kullanı<PERSON><PERSON> Ekle' : 'Kullanıcı Düzenle'} + ' - Stok Takip Sistemi'">Kullanıcı Ekle/Düzenle - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 th:text="${user.id == null ? 'Kullanı<PERSON><PERSON> Ekle' : 'Kullanıcı Düzenle'}">Kullanı<PERSON><PERSON> Ekle/Düzenle</h1>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form th:action="@{${user.id == null ? '/admin/users' : '/admin/users/update'}}" th:object="${user}" method="post">
                            <input type="hidden" th:field="*{id}">

                            <div class="mb-3">
                                <label for="username" class="form-label">Kullanıcı Adı</label>
                                <input type="text" class="form-control" id="username" th:field="*{username}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('username')}" th:errors="*{username}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Şifre</label>
                                <input type="password" class="form-control" id="password" th:field="*{password}" th:required="${user.id == null}">
                                <div class="form-text" th:if="${user.id != null}">Düzenleme sırasında boş bırakırsanız şifre değişmez.</div>
                                <div class="text-danger" th:if="${#fields.hasErrors('password')}" th:errors="*{password}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="firstName" class="form-label">Ad</label>
                                <input type="text" class="form-control" id="firstName" th:field="*{firstName}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('firstName')}" th:errors="*{firstName}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="lastName" class="form-label">Soyad</label>
                                <input type="text" class="form-control" id="lastName" th:field="*{lastName}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('lastName')}" th:errors="*{lastName}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta</label>
                                <input type="email" class="form-control" id="email" th:field="*{email}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isAdmin" name="isAdmin" th:checked="${isAdmin}">
                                    <label class="form-check-label" for="isAdmin">
                                        Admin Yetkisi
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="active" th:field="*{active}">
                                    <label class="form-check-label" for="active">
                                        Aktif
                                    </label>
                                </div>
                                <div class="text-danger" th:if="${#fields.hasErrors('active')}" th:errors="*{active}"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/admin/users}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
