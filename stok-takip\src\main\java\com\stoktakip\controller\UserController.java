package com.stoktakip.controller;

import com.stoktakip.model.User;
import com.stoktakip.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Set;

@Controller
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping("/register")
    public String registerUser(@Valid @ModelAttribute("user") User user,
                              BindingResult result,
                              RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "register";
        }

        try {
            userService.save(user);
            redirectAttributes.addFlashAttribute("successMessage", "Kayıt başarılı. Lütfen giriş yapın.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
            return "redirect:/register";
        }

        return "redirect:/login";
    }

    @GetMapping("/profile")
    public String viewProfile(Authentication authentication, Model model) {
        User user = userService.findByUsername(authentication.getName());
        model.addAttribute("user", user);
        return "users/profile";
    }

    @PostMapping("/profile/update")
    public String updateProfile(@Valid @ModelAttribute("user") User user,
                               BindingResult result,
                               RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "users/profile";
        }

        try {
            userService.update(user);
            redirectAttributes.addFlashAttribute("successMessage", "Profil başarıyla güncellendi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/profile";
    }

    @GetMapping("/profile/change-password")
    public String changePasswordForm() {
        return "users/change-password";
    }

    @PostMapping("/profile/change-password")
    public String changePassword(@RequestParam String oldPassword,
                                @RequestParam String newPassword,
                                @RequestParam String confirmPassword,
                                Authentication authentication,
                                RedirectAttributes redirectAttributes) {

        if (!newPassword.equals(confirmPassword)) {
            redirectAttributes.addFlashAttribute("errorMessage", "Yeni şifreler eşleşmiyor.");
            return "redirect:/profile/change-password";
        }

        try {
            User user = userService.findByUsername(authentication.getName());
            userService.changePassword(user.getId(), oldPassword, newPassword);
            redirectAttributes.addFlashAttribute("successMessage", "Şifre başarıyla değiştirildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
            return "redirect:/profile/change-password";
        }

        return "redirect:/profile";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/admin/users")
    public String listUsers(Model model) {
        model.addAttribute("users", userService.findAll());
        return "admin/users/list";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/admin/users/new")
    public String newUserForm(Model model) {
        model.addAttribute("user", new User());
        return "admin/users/form";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/admin/users")
    public String saveUser(@Valid @ModelAttribute("user") User user,
                          BindingResult result,
                          @RequestParam(required = false) boolean isAdmin,
                          RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "admin/users/form";
        }

        try {
            if (isAdmin) {
                user.setRoles(Set.of("ROLE_USER", "ROLE_ADMIN"));
            } else {
                user.setRoles(Set.of("ROLE_USER"));
            }

            userService.save(user);
            redirectAttributes.addFlashAttribute("successMessage", "Kullanıcı başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/admin/users";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/admin/users/edit/{id}")
    public String editUserForm(@PathVariable Long id, Model model) {
        User user = userService.findById(id);
        model.addAttribute("user", user);
        model.addAttribute("isAdmin", user.getRoles().contains("ROLE_ADMIN"));
        return "admin/users/form";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/admin/users/update")
    public String updateUser(@Valid @ModelAttribute("user") User user,
                            BindingResult result,
                            @RequestParam(required = false) boolean isAdmin,
                            RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return "admin/users/form";
        }

        try {
            if (isAdmin) {
                user.setRoles(Set.of("ROLE_USER", "ROLE_ADMIN"));
            } else {
                user.setRoles(Set.of("ROLE_USER"));
            }

            userService.update(user);
            redirectAttributes.addFlashAttribute("successMessage", "Kullanıcı başarıyla güncellendi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/admin/users";
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/admin/users/delete/{id}")
    public String deleteUser(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            userService.delete(id);
            redirectAttributes.addFlashAttribute("successMessage", "Kullanıcı başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/admin/users";
    }
}
