package com.stoktakip.service;

import com.stoktakip.model.Product;
import com.stoktakip.model.StockMovement;
import com.stoktakip.model.User;
import com.stoktakip.repository.StockMovementRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class StockMovementService {

    private final StockMovementRepository stockMovementRepository;
    private final ProductService productService;
    private final UserService userService;

    @Autowired
    public StockMovementService(StockMovementRepository stockMovementRepository,
                               ProductService productService,
                               UserService userService) {
        this.stockMovementRepository = stockMovementRepository;
        this.productService = productService;
        this.userService = userService;
    }

    public List<StockMovement> findAll() {
        return stockMovementRepository.findAll();
    }

    public StockMovement findById(Long id) {
        return stockMovementRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Stok hareketi bulunamadı: " + id));
    }

    public List<StockMovement> findByProduct(Long productId) {
        Product product = productService.findById(productId);
        return stockMovementRepository.findByProduct(product);
    }

    public List<StockMovement> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return stockMovementRepository.findByDateBetween(startDate, endDate);
    }

    public List<StockMovement> findByProductAndDateRange(Long productId, LocalDateTime startDate, LocalDateTime endDate) {
        Product product = productService.findById(productId);
        return stockMovementRepository.findByProductAndDateBetween(product, startDate, endDate);
    }

    public List<StockMovement> findByMovementType(StockMovement.MovementType movementType) {
        return stockMovementRepository.findByMovementType(movementType);
    }

    @Transactional
    public StockMovement save(StockMovement stockMovement, String username) {
        if (stockMovement.getProduct() == null || stockMovement.getProduct().getId() == null) {
            throw new IllegalArgumentException("Ürün bilgisi gereklidir");
        }

        Product product = productService.findById(stockMovement.getProduct().getId());
        stockMovement.setProduct(product);

        User user = userService.findByUsername(username);
        stockMovement.setUser(user);

        if (stockMovement.getDate() == null) {
            stockMovement.setDate(LocalDateTime.now());
        }

        // Stok miktarını güncelle
        int quantityChange = stockMovement.getQuantity();
        if (stockMovement.getMovementType() == StockMovement.MovementType.OUT) {
            quantityChange = -quantityChange;

            // Stok kontrolü
            if (product.getStockQuantity() < stockMovement.getQuantity()) {
                throw new IllegalStateException("Yetersiz stok. Mevcut: " + product.getStockQuantity() + ", İstenen: " + stockMovement.getQuantity());
            }
        }

        productService.updateStock(product.getId(), quantityChange);

        return stockMovementRepository.save(stockMovement);
    }

    @Transactional
    public void delete(Long id) {
        StockMovement stockMovement = findById(id);

        // Stok miktarını geri al
        int quantityChange = stockMovement.getQuantity();
        if (stockMovement.getMovementType() == StockMovement.MovementType.IN) {
            quantityChange = -quantityChange;
        } else {
            // Stok kontrolü
            Product product = stockMovement.getProduct();
            if (product.getStockQuantity() < quantityChange) {
                throw new IllegalStateException("Bu hareket silinemez. Mevcut stok: " + product.getStockQuantity() + ", Geri alınacak: " + quantityChange);
            }
        }

        productService.updateStock(stockMovement.getProduct().getId(), quantityChange);

        stockMovementRepository.delete(stockMovement);
    }
}
