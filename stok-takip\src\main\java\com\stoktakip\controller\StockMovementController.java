package com.stoktakip.controller;

import com.stoktakip.model.StockMovement;
import com.stoktakip.service.ProductService;
import com.stoktakip.service.StockMovementService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Controller
@RequestMapping("/stock-movements")
public class StockMovementController {

    private final StockMovementService stockMovementService;
    private final ProductService productService;

    @Autowired
    public StockMovementController(StockMovementService stockMovementService, ProductService productService) {
        this.stockMovementService = stockMovementService;
        this.productService = productService;
    }

    @GetMapping
    public String listStockMovements(Model model) {
        model.addAttribute("stockMovements", stockMovementService.findAll());
        return "stock-movements/list";
    }

    @GetMapping("/new")
    public String newStockMovementForm(Model model) {
        model.addAttribute("stockMovement", new StockMovement());
        model.addAttribute("products", productService.findAll());
        model.addAttribute("movementTypes", StockMovement.MovementType.values());
        return "stock-movements/form";
    }

    @PostMapping
    public String saveStockMovement(@Valid @ModelAttribute("stockMovement") StockMovement stockMovement,
                                   BindingResult result,
                                   RedirectAttributes redirectAttributes,
                                   Authentication authentication,
                                   Model model) {
        if (result.hasErrors()) {
            model.addAttribute("products", productService.findAll());
            model.addAttribute("movementTypes", StockMovement.MovementType.values());
            return "stock-movements/form";
        }

        try {
            stockMovementService.save(stockMovement, authentication.getName());
            redirectAttributes.addFlashAttribute("successMessage", "Stok hareketi başarıyla kaydedildi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/stock-movements";
    }

    @GetMapping("/delete/{id}")
    public String deleteStockMovement(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            stockMovementService.delete(id);
            redirectAttributes.addFlashAttribute("successMessage", "Stok hareketi başarıyla silindi.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "Hata: " + e.getMessage());
        }

        return "redirect:/stock-movements";
    }

    @GetMapping("/filter")
    public String filterStockMovements(@RequestParam(required = false) Long productId,
                                      @RequestParam(required = false) String movementType,
                                      @RequestParam(required = false) LocalDate startDate,
                                      @RequestParam(required = false) LocalDate endDate,
                                      Model model) {

        LocalDateTime startDateTime = startDate != null ?
                LocalDateTime.of(startDate, LocalTime.MIN) :
                LocalDateTime.of(LocalDate.now().minusMonths(1), LocalTime.MIN);

        LocalDateTime endDateTime = endDate != null ?
                LocalDateTime.of(endDate, LocalTime.MAX) :
                LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        if (productId != null) {
            if (movementType != null && !movementType.isEmpty()) {
                StockMovement.MovementType type = StockMovement.MovementType.valueOf(movementType);
                model.addAttribute("stockMovements", stockMovementService.findByProductAndDateRange(productId, startDateTime, endDateTime)
                        .stream()
                        .filter(m -> m.getMovementType() == type)
                        .toList());
            } else {
                model.addAttribute("stockMovements", stockMovementService.findByProductAndDateRange(productId, startDateTime, endDateTime));
            }
            model.addAttribute("productName", productService.findById(productId).getName());
        } else if (movementType != null && !movementType.isEmpty()) {
            StockMovement.MovementType type = StockMovement.MovementType.valueOf(movementType);
            model.addAttribute("stockMovements", stockMovementService.findByDateRange(startDateTime, endDateTime)
                    .stream()
                    .filter(m -> m.getMovementType() == type)
                    .toList());
        } else {
            model.addAttribute("stockMovements", stockMovementService.findByDateRange(startDateTime, endDateTime));
        }

        model.addAttribute("products", productService.findAll());
        model.addAttribute("movementTypes", StockMovement.MovementType.values());
        model.addAttribute("selectedProductId", productId);
        model.addAttribute("selectedMovementType", movementType);
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);

        return "stock-movements/filter";
    }
}
