package com.stoktakip.service;

import com.stoktakip.model.Category;
import com.stoktakip.model.Product;
import com.stoktakip.repository.ProductRepository;
import jakarta.persistence.EntityExistsException;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ProductService {

    private final ProductRepository productRepository;
    private final CategoryService categoryService;

    @Autowired
    public ProductService(ProductRepository productRepository, CategoryService categoryService) {
        this.productRepository = productRepository;
        this.categoryService = categoryService;
    }

    public List<Product> findAll() {
        return productRepository.findAll();
    }

    public Product findById(Long id) {
        return productRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Ürün bulunamadı: " + id));
    }

    public Product findByCode(String code) {
        return productRepository.findByCode(code)
                .orElseThrow(() -> new EntityNotFoundException("Ürün bulunamadı: " + code));
    }

    public List<Product> findByCategory(Long categoryId) {
        Category category = categoryService.findById(categoryId);
        return productRepository.findByCategory(category);
    }

    public List<Product> findByNameContaining(String name) {
        return productRepository.findByNameContainingIgnoreCase(name);
    }

    public List<Product> findLowStock(Integer threshold) {
        return productRepository.findByStockQuantityLessThan(threshold);
    }

    @Transactional
    public Product save(Product product) {
        if (product.getId() != null && productRepository.existsById(product.getId())) {
            throw new EntityExistsException("Bu ID ile ürün zaten mevcut: " + product.getId());
        }

        if (productRepository.existsByCode(product.getCode())) {
            throw new EntityExistsException("Bu kod ile ürün zaten mevcut: " + product.getCode());
        }

        if (product.getCategory() != null && product.getCategory().getId() != null) {
            Category category = categoryService.findById(product.getCategory().getId());
            product.setCategory(category);
        }

        return productRepository.save(product);
    }

    @Transactional
    public Product update(Product product) {
        if (product.getId() == null || !productRepository.existsById(product.getId())) {
            throw new EntityNotFoundException("Güncellenecek ürün bulunamadı: " + product.getId());
        }

        productRepository.findByCode(product.getCode())
                .ifPresent(existingProduct -> {
                    if (!existingProduct.getId().equals(product.getId())) {
                        throw new EntityExistsException("Bu kod ile başka bir ürün zaten mevcut: " + product.getCode());
                    }
                });

        if (product.getCategory() != null && product.getCategory().getId() != null) {
            Category category = categoryService.findById(product.getCategory().getId());
            product.setCategory(category);
        }

        return productRepository.save(product);
    }

    @Transactional
    public void delete(Long id) {
        Product product = findById(id);
        if (!product.getStockMovements().isEmpty()) {
            throw new IllegalStateException("Bu ürüne ait stok hareketleri bulunmaktadır. Önce stok hareketlerini silmelisiniz.");
        }
        productRepository.delete(product);
    }

    @Transactional
    public void updateStock(Long productId, Integer quantity) {
        Product product = findById(productId);
        product.setStockQuantity(product.getStockQuantity() + quantity);
        productRepository.save(product);
    }
}
