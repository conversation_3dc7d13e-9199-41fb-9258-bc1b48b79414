package com.stoktakip.service;

import com.stoktakip.model.User;
import com.stoktakip.repository.UserRepository;
import jakarta.persistence.EntityExistsException;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Service
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public List<User> findAll() {
        return userRepository.findAll();
    }

    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Kullanıcı bulunamadı: " + id));
    }

    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new EntityNotFoundException("Kullanıcı bulunamadı: " + username));
    }

    public User findByEmail(String email) {
        return userRepository.findByEmail(email)
                .orElseThrow(() -> new EntityNotFoundException("Kullanıcı bulunamadı: " + email));
    }

    @Transactional
    public User save(User user) {
        if (user.getId() != null && userRepository.existsById(user.getId())) {
            throw new EntityExistsException("Bu ID ile kullanıcı zaten mevcut: " + user.getId());
        }

        if (userRepository.existsByUsername(user.getUsername())) {
            throw new EntityExistsException("Bu kullanıcı adı zaten kullanılıyor: " + user.getUsername());
        }

        if (user.getEmail() != null && userRepository.existsByEmail(user.getEmail())) {
            throw new EntityExistsException("Bu e-posta adresi zaten kullanılıyor: " + user.getEmail());
        }

        // Şifreyi hashle
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Varsayılan rol ata
        if (user.getRoles() == null || user.getRoles().isEmpty()) {
            user.setRoles(Set.of("ROLE_USER"));
        }

        return userRepository.save(user);
    }

    @Transactional
    public User update(User user) {
        if (user.getId() == null || !userRepository.existsById(user.getId())) {
            throw new EntityNotFoundException("Güncellenecek kullanıcı bulunamadı: " + user.getId());
        }

        User existingUser = findById(user.getId());

        userRepository.findByUsername(user.getUsername())
                .ifPresent(u -> {
                    if (!u.getId().equals(user.getId())) {
                        throw new EntityExistsException("Bu kullanıcı adı zaten kullanılıyor: " + user.getUsername());
                    }
                });

        if (user.getEmail() != null) {
            userRepository.findByEmail(user.getEmail())
                    .ifPresent(u -> {
                        if (!u.getId().equals(user.getId())) {
                            throw new EntityExistsException("Bu e-posta adresi zaten kullanılıyor: " + user.getEmail());
                        }
                    });
        }

        // Şifre değiştirilmişse hashle
        if (user.getPassword() != null && !user.getPassword().isEmpty() &&
                !user.getPassword().equals(existingUser.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        } else {
            user.setPassword(existingUser.getPassword());
        }

        return userRepository.save(user);
    }

    @Transactional
    public void delete(Long id) {
        User user = findById(id);
        if (!user.getStockMovements().isEmpty()) {
            throw new IllegalStateException("Bu kullanıcıya ait stok hareketleri bulunmaktadır. Önce stok hareketlerini silmelisiniz.");
        }
        userRepository.delete(user);
    }

    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = findById(userId);

        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new IllegalArgumentException("Mevcut şifre yanlış");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }
}
