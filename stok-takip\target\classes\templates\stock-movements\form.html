<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Stok Hareketi Ekle - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1>Stok Hareketi Ekle</h1>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <form th:action="@{/stock-movements}" th:object="${stockMovement}" method="post">
                            <div class="mb-3">
                                <label for="product" class="form-label">Ürün</label>
                                <select class="form-select" id="product" th:field="*{product.id}" required>
                                    <option value=""><PERSON><PERSON><PERSON><PERSON></option>
                                    <option th:each="prod : ${products}" th:value="${prod.id}"
                                            th:text="${prod.name + ' (Stok: ' + prod.stockQuantity + ')'}">Ürün</option>
                                </select>
                                <div class="text-danger" th:if="${#fields.hasErrors('product')}" th:errors="*{product}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="movementType" class="form-label">Hareket Tipi</label>
                                <select class="form-select" id="movementType" th:field="*{movementType}" required>
                                    <option value="">Hareket Tipi Seçin</option>
                                    <option th:each="type : ${movementTypes}" th:value="${type}" th:text="${type.displayName}">Hareket Tipi</option>
                                </select>
                                <div class="text-danger" th:if="${#fields.hasErrors('movementType')}" th:errors="*{movementType}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="quantity" class="form-label">Miktar</label>
                                <input type="number" class="form-control" id="quantity" th:field="*{quantity}" min="1" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('quantity')}" th:errors="*{quantity}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="date" class="form-label">Tarih</label>
                                <input type="datetime-local" class="form-control" id="date" th:field="*{date}" required>
                                <div class="text-danger" th:if="${#fields.hasErrors('date')}" th:errors="*{date}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="note" class="form-label">Not</label>
                                <textarea class="form-control" id="note" th:field="*{note}" rows="3"></textarea>
                                <div class="text-danger" th:if="${#fields.hasErrors('note')}" th:errors="*{note}"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/stock-movements}" class="btn btn-secondary">İptal</a>
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script layout:fragment="script">
        // Sayfa yüklendiğinde tarih alanını şu anki tarih ve saat ile doldur
        document.addEventListener('DOMContentLoaded', function() {
            const dateField = document.getElementById('date');
            if (!dateField.value) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');

                const formattedDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;
                dateField.value = formattedDateTime;
            }
        });
    </script>
</body>
</html>
