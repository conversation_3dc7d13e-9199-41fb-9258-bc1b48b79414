<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Stok Hareketleri - Stok Takip Sistemi</title>
    <style>
        /* Stok Hareketleri Sayfası Özel CSS */
        .movements-container {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .movements-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .movements-title {
            color: #2d3436;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .movements-title i {
            margin-right: 1rem;
            color: #a29bfe;
        }

        .add-movement-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .add-movement-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .filter-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .filter-header {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .filter-body {
            padding: 2rem;
        }

        .filter-input {
            border: 2px solid #ddd;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .filter-input:focus {
            border-color: #a29bfe;
            box-shadow: 0 0 0 3px rgba(162, 155, 254, 0.1);
            background: white;
        }

        .filter-btn {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(162, 155, 254, 0.4);
        }

        .clear-btn {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .movements-table-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .table-enhanced {
            margin: 0;
            background: transparent;
        }

        .table-enhanced thead {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
        }

        .table-enhanced thead th {
            border: none;
            padding: 1.5rem 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .table-enhanced tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table-enhanced tbody tr:hover {
            background: rgba(162, 155, 254, 0.1);
            transform: scale(1.01);
        }

        .table-enhanced tbody td {
            padding: 1.5rem 1rem;
            vertical-align: middle;
            border: none;
        }

        .movement-id {
            font-family: 'Courier New', monospace;
            background: rgba(162, 155, 254, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
            font-weight: 600;
            color: #6c5ce7;
        }

        .movement-date {
            font-weight: 600;
            color: #2d3436;
        }

        .product-name {
            font-weight: 600;
            color: #2d3436;
            font-size: 1.1rem;
        }

        .movement-in {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .movement-out {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .quantity-display {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2d3436;
        }

        .user-badge {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .action-btn-delete {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #636e72;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #2d3436;
        }

        /* Animasyonlar */
        .movements-header {
            animation: slideInDown 0.6s ease-out;
        }

        .filter-card {
            animation: slideInLeft 0.6s ease-out;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .movements-table-card {
            animation: slideInUp 0.6s ease-out;
            animation-delay: 0.4s;
            animation-fill-mode: both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .movements-container {
                padding: 1rem;
            }

            .movements-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="movements-container">
            <div class="container">
                <!-- Header -->
                <div class="movements-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="movements-title">
                            <i class="fas fa-exchange-alt"></i>Stok Hareketleri
                        </h1>
                        <a th:href="@{/stock-movements/new}" class="add-movement-btn">
                            <i class="fas fa-plus me-2"></i>Yeni Hareket Ekle
                        </a>
                    </div>
                </div>

                <!-- Filtre -->
                <div class="filter-card">
                    <div class="filter-header">
                        <i class="fas fa-filter me-2"></i>Gelişmiş Filtreleme
                    </div>
                    <div class="filter-body">
                        <form th:action="@{/stock-movements/filter}" method="get">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="product" class="form-label">
                                        <i class="fas fa-box me-1"></i>Ürün
                                    </label>
                                    <select class="form-control filter-input" id="product" name="productId">
                                        <option value="">Tüm Ürünler</option>
                                        <option th:each="prod : ${allProducts}" th:value="${prod.id}" th:text="${prod.name}"
                                                th:selected="${productId != null && productId == prod.id}">Ürün</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="movementType" class="form-label">
                                        <i class="fas fa-arrows-alt me-1"></i>Hareket Tipi
                                    </label>
                                    <select class="form-control filter-input" id="movementType" name="movementType">
                                        <option value="">Tümü</option>
                                        <option th:each="type : ${T(com.stoktakip.model.StockMovement.MovementType).values()}"
                                                th:value="${type}" th:text="${type.displayName}"
                                                th:selected="${movementType != null && movementType == type}">Hareket Tipi</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="startDate" class="form-label">
                                        <i class="fas fa-calendar-alt me-1"></i>Başlangıç Tarihi
                                    </label>
                                    <input type="date" class="form-control filter-input" id="startDate" name="startDate" th:value="${startDate}">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="endDate" class="form-label">
                                        <i class="fas fa-calendar-check me-1"></i>Bitiş Tarihi
                                    </label>
                                    <input type="date" class="form-control filter-input" id="endDate" name="endDate" th:value="${endDate}">
                                </div>
                            </div>
                            <div class="d-flex gap-3 justify-content-end">
                                <button type="submit" class="btn filter-btn">
                                    <i class="fas fa-search me-2"></i>Filtrele
                                </button>
                                <a th:href="@{/stock-movements}" class="btn clear-btn">
                                    <i class="fas fa-times me-2"></i>Temizle
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Tablo -->
                <div class="movements-table-card">
                    <div th:if="${stockMovements.empty}" class="empty-state">
                        <i class="fas fa-clipboard-list"></i>
                        <h3>Henüz stok hareketi yok</h3>
                        <p>İlk stok hareketinizi ekleyerek başlayın.</p>
                        <a th:href="@{/stock-movements/new}" class="add-movement-btn">
                            <i class="fas fa-plus me-2"></i>İlk Hareketi Ekle
                        </a>
                    </div>

                    <div th:unless="${stockMovements.empty}" class="table-responsive">
                        <table class="table table-enhanced">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tarih</th>
                                    <th>Ürün</th>
                                    <th>Hareket Tipi</th>
                                    <th>Miktar</th>
                                    <th>Kullanıcı</th>
                                    <th>Not</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="movement : ${stockMovements}">
                                    <td>
                                        <span class="movement-id" th:text="${movement.id}">1</span>
                                    </td>
                                    <td>
                                        <div class="movement-date" th:text="${#temporals.format(movement.date, 'dd.MM.yyyy')}">01.01.2023</div>
                                        <small class="text-muted" th:text="${#temporals.format(movement.date, 'HH:mm')}">10:00</small>
                                    </td>
                                    <td>
                                        <div class="product-name" th:text="${movement.product.name}">Ürün</div>
                                        <small class="text-muted" th:text="${movement.product.code}">Kod</small>
                                    </td>
                                    <td>
                                        <span th:class="${movement.movementType.name() == 'IN' ? 'movement-in' : 'movement-out'}"
                                              th:text="${movement.movementType.displayName}">
                                            <i th:class="${movement.movementType.name() == 'IN' ? 'fas fa-arrow-down me-1' : 'fas fa-arrow-up me-1'}"></i>
                                            Giriş/Çıkış
                                        </span>
                                    </td>
                                    <td>
                                        <div class="quantity-display" th:text="${movement.quantity}">0</div>
                                    </td>
                                    <td>
                                        <span class="user-badge" th:text="${movement.user.username}">admin</span>
                                    </td>
                                    <td>
                                        <span th:text="${movement.note ?: 'Not yok'}" class="text-muted">Not</span>
                                    </td>
                                    <td>
                                        <a th:href="@{/stock-movements/delete/{id}(id=${movement.id})}"
                                           class="action-btn action-btn-delete"
                                           title="Sil"
                                           onclick="return confirm('Bu stok hareketini silmek istediğinize emin misiniz?')">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
