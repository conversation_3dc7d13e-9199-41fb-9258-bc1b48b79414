<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title th:text="${category.id == null ? 'Kategor<PERSON>' : 'Kategori <PERSON>'} + ' - Stok Takip Sistemi'">Kate<PERSON><PERSON>/Düzenle - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0">
                <i class="fas fa-tags me-2"></i>
                <span th:text="${category.id == null ? 'Kategori Ekle' : 'Kategori Düzenle'}">Kate<PERSON><PERSON>/<PERSON></span>
            </h1>
            <a th:href="@{/categories}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><PERSON><PERSON>
            </a>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            <span th:text="${category.id == null ? 'Yeni Kategori Bilgileri' : 'Kategori Bilgilerini Düzenle'}">Kategori Bilgileri</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{${category.id == null ? '/categories' : '/categories/update'}}" th:object="${category}" method="post">
                            <input type="hidden" th:field="*{id}">

                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag me-1"></i>Kategori Adı <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="name"
                                       th:field="*{name}"
                                       th:classappend="${#fields.hasErrors('name')} ? 'is-invalid' : ''"
                                       placeholder="Kategori adını giriniz"
                                       required>
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('name')}" th:errors="*{name}"></div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left me-1"></i>Açıklama
                                </label>
                                <textarea class="form-control"
                                          id="description"
                                          th:field="*{description}"
                                          th:classappend="${#fields.hasErrors('description')} ? 'is-invalid' : ''"
                                          rows="4"
                                          placeholder="Kategori açıklamasını giriniz (isteğe bağlı)"></textarea>
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('description')}" th:errors="*{description}"></div>
                                <div class="form-text">Bu alan isteğe bağlıdır.</div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a th:href="@{/categories}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>İptal
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <span th:text="${category.id == null ? 'Kaydet' : 'Güncelle'}">Kaydet</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
