<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Kategoriler - Stok Takip Sistemi</title>
    <style>
        /* Kategoriler Sayfası Özel CSS */
        .categories-container {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            min-height: 100vh;
            padding: 2rem 0;
            border-radius: 20px;
            align-self: auto;
            margin: auto;
        }

        .categories-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .categories-title {
            color: #2d3436;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .categories-title i {
            margin-right: 1rem;
            color: #00b894;
        }

        .add-category-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
        }

        .add-category-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .categories-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .categories-card-header {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
        }

        .categories-card-header i {
            margin-right: 0.5rem;
        }

        .categories-card-body {
            padding: 2rem;
        }

        .table-enhanced {
            margin: 0;
            border-radius: 10px;
            overflow: hidden;
        }

        .table-enhanced thead {
            background: linear-gradient(135deg, #ddd 0%, #bbb 100%);
        }

        .table-enhanced thead th {
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: #2d3436;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table-enhanced tbody tr {
            transition: all 0.3s ease;
        }

        .table-enhanced tbody tr:hover {
            background: rgba(0, 184, 148, 0.1);
            transform: scale(1.01);
        }

        .table-enhanced tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .badge-enhanced {
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .badge-primary-enhanced {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .btn-group-enhanced {
            display: flex;
            gap: 0.5rem;
        }

        .btn-enhanced {
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
        }

        .btn-edit-enhanced {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .btn-edit-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
            color: white;
        }

        .btn-view-enhanced {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }

        .btn-view-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
            color: white;
        }

        .btn-delete-enhanced {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }

        .btn-delete-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(253, 121, 168, 0.3);
            color: white;
        }

        .alert-enhanced {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-info-enhanced {
            border-left: 4px solid #74b9ff;
        }

        .alert-link-enhanced {
            color: #0984e3;
            font-weight: 600;
            text-decoration: none;
        }

        .alert-link-enhanced:hover {
            color: #74b9ff;
        }

        /* Animasyonlar */
        .categories-header {
            animation: slideInDown 0.6s ease-out;
        }

        .categories-card {
            animation: slideInUp 0.6s ease-out;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .categories-container {
                padding: 1rem;
            }

            .categories-title {
                font-size: 2rem;
            }

            .categories-header {
                flex-direction: column;
                gap: 1rem;
            }

            .btn-group-enhanced {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="categories-container">
            <div class="container">
                <!-- Header -->
                <div class="categories-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="categories-title">
                            <i class="fas fa-tags"></i>Kategoriler
                        </h1>
                        <a th:href="@{/categories/new}" class="add-category-btn">
                            <i class="fas fa-plus me-2"></i>Yeni Kategori Ekle
                        </a>
                    </div>
                </div>

                <!-- Categories Card -->
                <div class="categories-card">
                    <div class="categories-card-header">
                        <i class="fas fa-list"></i>Kategori Listesi
                    </div>
                    <div class="categories-card-body">
                        <div th:if="${categories.empty}" class="alert-enhanced alert-info-enhanced">
                            <i class="fas fa-info-circle me-2"></i>Henüz kategori bulunmamaktadır.
                            <a th:href="@{/categories/new}" class="alert-link-enhanced">İlk kategoriyi ekleyin</a>.
                        </div>

                        <div th:unless="${categories.empty}" class="table-responsive">
                            <table class="table table-enhanced">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Kategori Adı</th>
                                        <th>Açıklama</th>
                                        <th>Ürün Sayısı</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="category : ${categories}">
                                        <td th:text="${category.id}">1</td>
                                        <td>
                                            <strong th:text="${category.name}">Kategori Adı</strong>
                                        </td>
                                        <td th:text="${category.description ?: 'Açıklama yok'}">Açıklama</td>
                                        <td>
                                            <span class="badge-enhanced badge-primary-enhanced" th:text="${category.products.size()}">0</span>
                                        </td>
                                        <td>
                                            <div class="btn-group-enhanced">
                                                <a th:href="@{/categories/edit/{id}(id=${category.id})}"
                                                   class="btn-enhanced btn-edit-enhanced"
                                                   title="Düzenle">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a th:href="@{/products/category/{id}(id=${category.id})}"
                                                   class="btn-enhanced btn-view-enhanced"
                                                   title="Ürünleri Görüntüle">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a th:href="@{/categories/delete/{id}(id=${category.id})}"
                                                   class="btn-enhanced btn-delete-enhanced"
                                                   title="Sil"
                                                   onclick="return confirm('Bu kategoriyi silmek istediğinizden emin misiniz?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
