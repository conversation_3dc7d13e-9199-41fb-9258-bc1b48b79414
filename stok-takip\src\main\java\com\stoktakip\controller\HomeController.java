package com.stoktakip.controller;

import com.stoktakip.model.User;
import com.stoktakip.service.ProductService;
import com.stoktakip.service.StockMovementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {

    private final ProductService productService;
    private final StockMovementService stockMovementService;

    @Autowired
    public HomeController(ProductService productService, StockMovementService stockMovementService) {
        this.productService = productService;
        this.stockMovementService = stockMovementService;
    }

    @GetMapping("/")
    public String home() {
        return "redirect:/dashboard";
    }

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("totalProducts", productService.findAll().size());
        model.addAttribute("lowStockProducts", productService.findLowStock(10));
        model.addAttribute("recentMovements", stockMovementService.findAll().stream()
                .sorted((m1, m2) -> m2.getDate().compareTo(m1.getDate()))
                .limit(5)
                .toList());
        return "dashboard";
    }

    @GetMapping("/login")
    public String login() {
        return "login";
    }

    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new User());
        return "register";
    }
}
