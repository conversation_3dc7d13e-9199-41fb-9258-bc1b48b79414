<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title th:text="${product.id == null ? '<PERSON><PERSON><PERSON><PERSON>' : '<PERSON><PERSON><PERSON><PERSON>'} + ' - Stok Takip Sistemi'"><PERSON><PERSON><PERSON><PERSON>/Düzenle - Stok Takip Sistemi</title>
    <style>
        /* Ürün Form Sayfası Özel CSS */
        .product-form-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .product-form-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .product-form-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .product-form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .product-form-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .product-form-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .product-form-header .subtitle {
            margin-top: 0.5rem;
            opacity: 0.9;
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
        }

        .product-form-body {
            padding: 3rem;
        }

        .form-group-enhanced {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-label-enhanced {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            font-size: 1rem;
        }

        .form-label-enhanced i {
            margin-right: 0.5rem;
            color: #667eea;
            font-size: 1.1rem;
        }

        .form-control-enhanced {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-control-enhanced:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            transform: translateY(-2px);
        }

        .form-control-enhanced:hover {
            border-color: #cbd5e0;
            background: white;
        }

        .input-group-enhanced {
            position: relative;
        }

        .input-group-enhanced .input-group-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            border-radius: 0 12px 12px 0;
        }

        .btn-enhanced {
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary-enhanced {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary-enhanced:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
            transform: translateY(-2px);
        }

        .back-button {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }

        .invalid-feedback-enhanced {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
        }

        .invalid-feedback-enhanced i {
            margin-right: 0.5rem;
        }

        .form-text-enhanced {
            color: #718096;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        /* Animasyonlar */
        .product-form-card {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group-enhanced {
            animation: fadeInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .form-group-enhanced:nth-child(1) { animation-delay: 0.1s; }
        .form-group-enhanced:nth-child(2) { animation-delay: 0.2s; }
        .form-group-enhanced:nth-child(3) { animation-delay: 0.3s; }
        .form-group-enhanced:nth-child(4) { animation-delay: 0.4s; }
        .form-group-enhanced:nth-child(5) { animation-delay: 0.5s; }
        .form-group-enhanced:nth-child(6) { animation-delay: 0.6s; }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .product-form-container {
                padding: 1rem;
            }

            .product-form-body {
                padding: 2rem 1.5rem;
            }

            .product-form-header h1 {
                font-size: 2rem;
            }

            .back-button {
                position: static;
                margin-bottom: 1rem;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <div layout:fragment="content">
        <div class="product-form-container">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 col-md-10">
                        <div class="product-form-card">
                            <div class="product-form-header">
                                <a th:href="@{/products}" class="back-button">
                                    <i class="fas fa-arrow-left me-2"></i>Geri Dön
                                </a>
                                <h1>
                                    <i class="fas fa-box me-3"></i>
                                    <span th:text="${product.id == null ? 'Yeni Ürün Ekle' : 'Ürün Bilgilerini Düzenle'}">Ürün Ekle/Düzenle</span>
                                </h1>
                                <div class="subtitle" th:text="${product.id == null ? 'Sisteme yeni bir ürün ekleyin' : 'Mevcut ürün bilgilerini güncelleyin'}">
                                    Ürün bilgilerini doldurun
                                </div>
                            </div>

                            <div class="product-form-body">
                                <form th:action="@{${product.id == null ? '/products' : '/products/update'}}" th:object="${product}" method="post">
                                    <input type="hidden" th:field="*{id}">

                                    <div class="form-group-enhanced">
                                        <label for="code" class="form-label-enhanced">
                                            <i class="fas fa-barcode"></i>Ürün Kodu <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control form-control-enhanced"
                                               id="code"
                                               th:field="*{code}"
                                               placeholder="Örn: PRD001"
                                               required>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('code')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{code}"></span>
                                        </div>
                                        <div class="form-text-enhanced">Benzersiz bir ürün kodu giriniz</div>
                                    </div>

                                    <div class="form-group-enhanced">
                                        <label for="name" class="form-label-enhanced">
                                            <i class="fas fa-tag"></i>Ürün Adı <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control form-control-enhanced"
                                               id="name"
                                               th:field="*{name}"
                                               placeholder="Ürün adını giriniz"
                                               required>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('name')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{name}"></span>
                                        </div>
                                    </div>

                                    <div class="form-group-enhanced">
                                        <label for="description" class="form-label-enhanced">
                                            <i class="fas fa-align-left"></i>Açıklama
                                        </label>
                                        <textarea class="form-control form-control-enhanced"
                                                  id="description"
                                                  th:field="*{description}"
                                                  rows="4"
                                                  placeholder="Ürün açıklamasını giriniz (isteğe bağlı)"></textarea>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('description')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{description}"></span>
                                        </div>
                                        <div class="form-text-enhanced">Bu alan isteğe bağlıdır</div>
                                    </div>

                                    <div class="form-group-enhanced">
                                        <label for="category" class="form-label-enhanced">
                                            <i class="fas fa-folder"></i>Kategori <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-control form-control-enhanced" id="category" th:field="*{category.id}" required>
                                            <option value="">Kategori Seçin</option>
                                            <option th:each="cat : ${categories}" th:value="${cat.id}" th:text="${cat.name}">Kategori</option>
                                        </select>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('category')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{category}"></span>
                                        </div>
                                    </div>

                                    <div class="form-group-enhanced">
                                        <label for="price" class="form-label-enhanced">
                                            <i class="fas fa-lira-sign"></i>Fiyat <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group input-group-enhanced">
                                            <input type="number"
                                                   class="form-control form-control-enhanced"
                                                   id="price"
                                                   th:field="*{price}"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00"
                                                   required>
                                            <span class="input-group-text">₺</span>
                                        </div>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('price')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{price}"></span>
                                        </div>
                                    </div>

                                    <div class="form-group-enhanced">
                                        <label for="stockQuantity" class="form-label-enhanced">
                                            <i class="fas fa-cubes"></i>Stok Miktarı <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control form-control-enhanced"
                                               id="stockQuantity"
                                               th:field="*{stockQuantity}"
                                               min="0"
                                               placeholder="0"
                                               required>
                                        <div class="invalid-feedback-enhanced" th:if="${#fields.hasErrors('stockQuantity')}">
                                            <i class="fas fa-exclamation-circle"></i>
                                            <span th:errors="*{stockQuantity}"></span>
                                        </div>
                                        <div class="form-text-enhanced">Mevcut stok miktarını giriniz</div>
                                    </div>

                                    <div class="d-flex gap-3 justify-content-end mt-4">
                                        <a th:href="@{/products}" class="btn btn-enhanced btn-secondary-enhanced">
                                            <i class="fas fa-times me-2"></i>İptal
                                        </a>
                                        <button type="submit" class="btn btn-enhanced btn-primary-enhanced">
                                            <i class="fas fa-save me-2"></i>
                                            <span th:text="${product.id == null ? 'Kaydet' : 'Güncelle'}">Kaydet</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
