<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: 'Stok Takip Sistemi'">Stok Takip Sistemi</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css?v=1.0}">

    <!-- Inline CSS (Fallback) -->
    <style>
        /* Main Styles */
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --white-color: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 0.25rem;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background-color: var(--primary-color) !important;
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white-color) !important;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-link {
            color: var(--white-color) !important;
        }

        .nav-link:hover {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
            transition: var(--transition);
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .dashboard-card-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .table th {
            background-color: var(--light-color);
        }

        .bg-primary, .badge-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-success, .badge-success {
            background-color: var(--success-color) !important;
        }

        .bg-danger, .badge-danger {
            background-color: var(--danger-color) !important;
        }

        .bg-warning, .badge-warning {
            background-color: var(--warning-color) !important;
        }

        .footer {
            background-color: var(--dark-color);
            color: var(--white-color);
            padding: 1rem 0;
            margin-top: auto;
        }
    </style>

    <!-- Additional CSS -->
    <th:block th:replace="${links} ?: ~{}"></th:block>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container navbar-container">
                <a class="navbar-brand" th:href="@{/}">
                    <i class="fas fa-boxes me-2"></i>Stok Takip
                </a>

                <button id="menu-toggle" class="navbar-toggler" type="button">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbar-nav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/dashboard}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/products}">
                                <i class="fas fa-box me-1"></i>Ürünler
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/categories}">
                                <i class="fas fa-tags me-1"></i>Kategoriler
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/stock-movements}">
                                <i class="fas fa-exchange-alt me-1"></i>Stok Hareketleri
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="hasRole('ADMIN')">
                            <a class="nav-link" th:href="@{/admin/users}">
                                <i class="fas fa-users me-1"></i>Kullanıcılar
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/profile}">
                                <i class="fas fa-user me-1"></i>Profil
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="isAuthenticated()">
                            <a class="nav-link" th:href="@{/logout}">
                                <i class="fas fa-sign-out-alt me-1"></i>Çıkış
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="!isAuthenticated()">
                            <a class="nav-link" th:href="@{/login}">
                                <i class="fas fa-sign-in-alt me-1"></i>Giriş
                            </a>
                        </li>
                        <li class="nav-item" sec:authorize="!isAuthenticated()">
                            <a class="nav-link" th:href="@{/register}">
                                <i class="fas fa-user-plus me-1"></i>Kayıt
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Flash Messages -->
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${successMessage}">İşlem başarılı</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${errorMessage}">Bir hata oluştu</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Page Title -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0" th:text="${pageTitle} ?: 'Stok Takip Sistemi'">Stok Takip Sistemi</h1>
                <div th:replace="${pageActions} ?: ~{}"></div>
            </div>

            <!-- Page Content -->
            <div layout:fragment="content">
                <!-- Default content will be replaced -->
                <p>Sayfa içeriği yükleniyor...</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <p class="mb-0">&copy; 2025 Stok Takip Sistemi. Tüm hakları saklıdır.</p>
                </div>
                <div>
                    <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JS -->
    <script th:src="@{/js/script.js?v=1.0}"></script>

    <!-- Additional Scripts -->
    <th:block th:replace="${scripts} ?: ~{}"></th:block>
</body>
</html>
