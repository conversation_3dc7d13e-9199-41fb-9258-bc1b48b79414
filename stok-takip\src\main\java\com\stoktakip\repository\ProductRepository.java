package com.stoktakip.repository;

import com.stoktakip.model.Category;
import com.stoktakip.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    List<Product> findByCategory(Category category);
    Optional<Product> findByCode(String code);
    boolean existsByCode(String code);
    List<Product> findByNameContainingIgnoreCase(String name);
    List<Product> findByStockQuantityLessThan(Integer quantity);
}
