<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}">
<head>
    <title>Kullanıcı Yönetimi - Stok Takip Sistemi</title>
</head>
<body>
    <div layout:fragment="content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1>Kullanıc<PERSON> Yönetimi</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <a th:href="@{/admin/users/new}" class="btn btn-sm btn-primary">
                    <i class="fas fa-user-plus"></i> Ye<PERSON>
                </a>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th><PERSON><PERSON><PERSON><PERSON><PERSON>ı</th>
                        <th>Ad</th>
                        <th>Soyad</th>
                        <th>E-posta</th>
                        <th>Roller</th>
                        <th>Durum</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="user : ${users}">
                        <td th:text="${user.id}">1</td>
                        <td th:text="${user.username}">admin</td>
                        <td th:text="${user.firstName}">Admin</td>
                        <td th:text="${user.lastName}">User</td>
                        <td th:text="${user.email}"><EMAIL></td>
                        <td>
                            <span th:each="role : ${user.roles}" th:class="${role == 'ROLE_ADMIN' ? 'badge bg-danger' : 'badge bg-primary'}"
                                  th:text="${role == 'ROLE_ADMIN' ? 'Admin' : 'Kullanıcı'}" class="me-1">Rol</span>
                        </td>
                        <td>
                            <span th:class="${user.active ? 'badge bg-success' : 'badge bg-danger'}"
                                  th:text="${user.active ? 'Aktif' : 'Pasif'}">Durum</span>
                        </td>
                        <td>
                            <a th:href="@{/admin/users/edit/{id}(id=${user.id})}" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a th:href="@{/admin/users/delete/{id}(id=${user.id})}"
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Bu kullanıcıyı silmek istediğinize emin misiniz?')">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </td>
                    </tr>
                    <tr th:if="${users.empty}">
                        <td colspan="8" class="text-center">Henüz kullanıcı bulunmamaktadır.</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
